import 'package:flutter/material.dart';
import 'calculator_logic.dart';

class CalculatorScreen extends StatefulWidget {
  const CalculatorScreen({super.key});

  @override
  State<CalculatorScreen> createState() => _CalculatorScreenState();
}

class _CalculatorScreenState extends State<CalculatorScreen> {
  final CalculatorLogic _logic = CalculatorLogic();
  String _display = '0';
  String _expression = '';

  void _onButtonPressed(String button) {
    setState(() {
      final result = _logic.processInput(button);
      _display = result.display;
      _expression = result.expression;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).colorScheme.background,
      body: SafeArea(
        child: Column(
          children: [
            // 显示区域
            Expanded(
              flex: 2,
              child: Container(
                width: double.infinity,
                padding: const EdgeInsets.all(24),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    // 表达式显示
                    Text(
                      _expression,
                      style: TextStyle(
                        fontSize: 24,
                        color: Theme.of(context).colorScheme.onBackground.withOpacity(0.6),
                      ),
                      textAlign: TextAlign.end,
                    ),
                    const SizedBox(height: 8),
                    // 结果显示
                    Text(
                      _display,
                      style: TextStyle(
                        fontSize: 48,
                        fontWeight: FontWeight.bold,
                        color: Theme.of(context).colorScheme.onBackground,
                      ),
                      textAlign: TextAlign.end,
                    ),
                  ],
                ),
              ),
            ),
            // 按钮区域
            Expanded(
              flex: 3,
              child: Container(
                padding: const EdgeInsets.all(16),
                child: Column(
                  children: [
                    // 第一行：清除、正负号、百分比、除号
                    Expanded(
                      child: Row(
                        children: [
                          _buildButton('C', Colors.red, isWide: false),
                          const SizedBox(width: 8),
                          _buildButton('±', Colors.orange, isWide: false),
                          const SizedBox(width: 8),
                          _buildButton('%', Colors.orange, isWide: false),
                          const SizedBox(width: 8),
                          _buildButton('÷', Colors.orange, isWide: false),
                        ],
                      ),
                    ),
                    const SizedBox(height: 8),
                    // 第二行：7、8、9、乘号
                    Expanded(
                      child: Row(
                        children: [
                          _buildButton('7', Colors.grey[300]!, isWide: false),
                          const SizedBox(width: 8),
                          _buildButton('8', Colors.grey[300]!, isWide: false),
                          const SizedBox(width: 8),
                          _buildButton('9', Colors.grey[300]!, isWide: false),
                          const SizedBox(width: 8),
                          _buildButton('×', Colors.orange, isWide: false),
                        ],
                      ),
                    ),
                    const SizedBox(height: 8),
                    // 第三行：4、5、6、减号
                    Expanded(
                      child: Row(
                        children: [
                          _buildButton('4', Colors.grey[300]!, isWide: false),
                          const SizedBox(width: 8),
                          _buildButton('5', Colors.grey[300]!, isWide: false),
                          const SizedBox(width: 8),
                          _buildButton('6', Colors.grey[300]!, isWide: false),
                          const SizedBox(width: 8),
                          _buildButton('-', Colors.orange, isWide: false),
                        ],
                      ),
                    ),
                    const SizedBox(height: 8),
                    // 第四行：1、2、3、加号
                    Expanded(
                      child: Row(
                        children: [
                          _buildButton('1', Colors.grey[300]!, isWide: false),
                          const SizedBox(width: 8),
                          _buildButton('2', Colors.grey[300]!, isWide: false),
                          const SizedBox(width: 8),
                          _buildButton('3', Colors.grey[300]!, isWide: false),
                          const SizedBox(width: 8),
                          _buildButton('+', Colors.orange, isWide: false),
                        ],
                      ),
                    ),
                    const SizedBox(height: 8),
                    // 第五行：0、小数点、等号
                    Expanded(
                      child: Row(
                        children: [
                          _buildButton('0', Colors.grey[300]!, isWide: true),
                          const SizedBox(width: 8),
                          _buildButton('.', Colors.grey[300]!, isWide: false),
                          const SizedBox(width: 8),
                          _buildButton('=', Colors.orange, isWide: false),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildButton(String text, Color color, {bool isWide = false}) {
    return Expanded(
      flex: isWide ? 2 : 1,
      child: Container(
        height: double.infinity,
        margin: const EdgeInsets.symmetric(horizontal: 4),
        child: ElevatedButton(
          onPressed: () => _onButtonPressed(text),
          style: ElevatedButton.styleFrom(
            backgroundColor: color,
            foregroundColor: color == Colors.grey[300]! 
                ? Colors.black 
                : Colors.white,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(16),
            ),
            elevation: 0,
          ),
          child: Text(
            text,
            style: const TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
      ),
    );
  }
}
